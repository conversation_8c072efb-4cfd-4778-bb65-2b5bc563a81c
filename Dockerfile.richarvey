FROM richarvey/nginx-php-fpm:latest

WORKDIR /var/www/html

RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    oniguruma-dev \
    nodejs \
    npm

RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN mkdir -p /var/www/html/storage /var/www/html/bootstrap/cache && \
    chown -R nginx:nginx /var/www/html && \
    chmod -R 755 /var/www/html && \
    chmod -R 777 /var/www/html/storage && \
    chmod -R 777 /var/www/html/bootstrap/cache

COPY nginx-default.conf /etc/nginx/http.d/default.conf

RUN echo "upload_max_filesize=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini && \
    echo "post_max_size=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini && \
    echo "memory_limit=256M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini && \
    echo "max_execution_time=300" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini

EXPOSE 80

COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["/bin/sh", "-c", "php-fpm && nginx -g 'daemon off;'"]
