FROM richarvey/nginx-php-fpm:latest

WORKDIR /var/www/html

RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    oniguruma-dev \
    nodejs \
    npm

RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN chown -R nginx:nginx /var/www/html && \
    chmod -R 755 /var/www/html && \
    chmod -R 777 /var/www/html/storage && \
    chmod -R 777 /var/www/html/bootstrap/cache

RUN echo 'server { \
    listen 80; \
    server_name localhost; \
    root /var/www/html/public; \
    index index.php index.html; \
    \
    location / { \
        try_files $uri $uri/ /index.php?$query_string; \
    } \
    \
    location ~ \.php$ { \
        fastcgi_pass 127.0.0.1:9000; \
        fastcgi_index index.php; \
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name; \
        include fastcgi_params; \
        fastcgi_buffers 16 16k; \
        fastcgi_buffer_size 32k; \
    } \
    \
    location ~ /\.ht { \
        deny all; \
    } \
}' > /etc/nginx/http.d/default.conf

RUN echo "upload_max_filesize=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini && \
    echo "post_max_size=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini && \
    echo "memory_limit=256M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini && \
    echo "max_execution_time=300" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini

EXPOSE 80

COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["/bin/sh", "-c", "php-fpm && nginx -g 'daemon off;'"]
