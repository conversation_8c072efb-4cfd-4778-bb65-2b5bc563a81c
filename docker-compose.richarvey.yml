version: '3'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web
    restart: always
    ports:
      - "8081:80"
    volumes:
      - ./heaven:/var/www/html
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=*************
      - DB_DATABASE=heaven
      - DB_USERNAME=yunyitang
      - DB_PASSWORD=R9fO26fcHj6ESRkLy
    networks:
      - heaven
    depends_on:
      - redis
  redis:
    image: redis:alpine
    container_name: heaven-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - heaven-redis:/data
    networks:
      - heaven
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s

networks:
  heaven:
    driver: bridge

volumes:
  heaven-redis:
    driver: local
